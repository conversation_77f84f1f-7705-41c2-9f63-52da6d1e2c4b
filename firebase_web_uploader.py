# -*- coding: utf-8 -*-
"""
Firebase Web API Uploader
رفع الملفات إلى Firebase Storage باستخدام Web API بدلاً من Admin SDK
"""

import os
import json
import time
import random
import string
import requests
from typing import Optional, Dict, Any
from urllib.parse import quote

class FirebaseWebUploader:
    """رافع ملفات Firebase باستخدام Web API"""
    
    def __init__(self):
        self.project_id = "download-e33a2"
        self.storage_bucket = "download-e33a2.firebasestorage.app"
        # استخدام رابط مباشر لـ Google Cloud Storage
        self.upload_url_base = f"https://storage.googleapis.com/{self.storage_bucket}"
        self.download_url_base = f"https://storage.googleapis.com/{self.storage_bucket}"
        
    def upload_file(self, file_content: bytes, file_name: str,
                   content_type: str = 'application/octet-stream',
                   folder: str = '') -> Optional[str]:
        """رفع ملف إلى Firebase Storage باستخدام طريقة مبسطة"""
        try:
            # إنشاء مسار الملف
            if folder:
                file_path = f"{folder}/{file_name}"
            else:
                file_path = file_name

            print(f"🔄 رفع الملف: {file_path}")
            print(f"📊 حجم الملف: {len(file_content) / (1024*1024):.2f} MB")

            # محاولة رفع الملف باستخدام طرق مختلفة

            # الطريقة 1: استخدام Firebase Storage REST API
            try:
                upload_url = f"https://firebasestorage.googleapis.com/v0/b/{self.storage_bucket}/o?name={quote(file_path, safe='')}"

                headers = {
                    'Content-Type': content_type,
                    'Content-Length': str(len(file_content))
                }

                response = requests.post(upload_url, data=file_content, headers=headers, timeout=120)

                if response.status_code in [200, 201]:
                    # إنشاء رابط التحميل
                    download_url = f"https://firebasestorage.googleapis.com/v0/b/{self.storage_bucket}/o/{quote(file_path, safe='')}?alt=media"

                    print(f"✅ تم رفع الملف بنجاح (Firebase REST API)!")
                    print(f"🔗 رابط التحميل: {download_url}")

                    return download_url
                else:
                    print(f"⚠️ فشل الرفع باستخدام Firebase REST API: HTTP {response.status_code}")

            except Exception as e1:
                print(f"⚠️ خطأ في Firebase REST API: {e1}")

            # الطريقة 2: استخدام Google Cloud Storage API مباشرة
            try:
                upload_url = f"https://storage.googleapis.com/upload/storage/v1/b/{self.storage_bucket}/o?uploadType=media&name={quote(file_path, safe='')}"

                headers = {
                    'Content-Type': content_type,
                    'Content-Length': str(len(file_content))
                }

                response = requests.post(upload_url, data=file_content, headers=headers, timeout=120)

                if response.status_code in [200, 201]:
                    # إنشاء رابط التحميل
                    download_url = f"https://storage.googleapis.com/{self.storage_bucket}/{file_path}"

                    print(f"✅ تم رفع الملف بنجاح (Google Cloud Storage)!")
                    print(f"🔗 رابط التحميل: {download_url}")

                    return download_url
                else:
                    print(f"⚠️ فشل الرفع باستخدام Google Cloud Storage: HTTP {response.status_code}")

            except Exception as e2:
                print(f"⚠️ خطأ في Google Cloud Storage API: {e2}")

            # إذا فشلت جميع الطرق
            print("❌ فشل رفع الملف بجميع الطرق المتاحة")
            return None

        except Exception as e:
            print(f"❌ خطأ عام في رفع الملف: {e}")
            return None
    
    def upload_mod(self, mod_content: bytes, original_filename: str) -> Optional[str]:
        """رفع ملف مود إلى Firebase Storage"""
        try:
            # إنشاء اسم ملف فريد
            timestamp = int(time.time())
            random_str = ''.join(random.choices(string.ascii_lowercase + string.digits, k=8))
            base_name, ext = os.path.splitext(original_filename)
            if not ext:
                ext = '.mcpack'
            
            # تنقيح اسم الملف
            safe_base_name = "".join(c for c in base_name if c.isalnum() or c in ('-', '_')).strip()
            if not safe_base_name:
                safe_base_name = "mod"
            
            unique_filename = f"{safe_base_name}_{timestamp}_{random_str}{ext}"
            
            return self.upload_file(
                mod_content,
                unique_filename,
                'application/octet-stream',
                'mods'
            )
            
        except Exception as e:
            print(f"❌ خطأ في رفع المود: {e}")
            return None
    
    def upload_image(self, image_content: bytes, original_filename: str) -> Optional[str]:
        """رفع صورة إلى Firebase Storage"""
        try:
            # تحديد نوع المحتوى
            content_type = 'image/jpeg'  # افتراضي
            if original_filename.lower().endswith('.png'):
                content_type = 'image/png'
            elif original_filename.lower().endswith('.gif'):
                content_type = 'image/gif'
            
            # إنشاء اسم ملف فريد
            timestamp = int(time.time())
            random_str = ''.join(random.choices(string.ascii_lowercase + string.digits, k=8))
            base_name, ext = os.path.splitext(original_filename)
            if not ext:
                ext = '.jpg'
            
            safe_base_name = "".join(c for c in base_name if c.isalnum() or c in ('-', '_')).strip()
            if not safe_base_name:
                safe_base_name = "image"
            
            unique_filename = f"img_{safe_base_name}_{timestamp}_{random_str}{ext}"
            
            return self.upload_file(
                image_content,
                unique_filename,
                content_type,
                'images'
            )
            
        except Exception as e:
            print(f"❌ خطأ في رفع الصورة: {e}")
            return None
    
    def test_connection(self) -> Dict[str, Any]:
        """اختبار الاتصال بـ Firebase Storage"""
        test_result = {
            'can_upload': False,
            'error_details': None
        }
        
        try:
            # اختبار رفع ملف صغير
            test_content = b"Firebase Web API test - " + str(int(time.time())).encode()
            test_filename = f"test/web_api_test_{int(time.time())}.txt"
            
            result = self.upload_file(test_content, test_filename, 'text/plain')
            
            if result:
                test_result['can_upload'] = True
                print("✅ اختبار Firebase Web API نجح!")
            else:
                test_result['error_details'] = "فشل رفع الملف التجريبي"
                
        except Exception as e:
            test_result['error_details'] = f"خطأ في اختبار Firebase Web API: {e}"
        
        return test_result

# إنشاء مثيل عام
firebase_web_uploader = FirebaseWebUploader()

def upload_to_firebase_web(file_content: bytes, file_name: str, 
                          content_type: str, file_type: str = "mod") -> Optional[str]:
    """دالة مساعدة لرفع الملفات باستخدام Firebase Web API"""
    try:
        if file_type == "mod":
            return firebase_web_uploader.upload_mod(file_content, file_name)
        elif file_type == "image":
            return firebase_web_uploader.upload_image(file_content, file_name)
        else:
            return firebase_web_uploader.upload_file(file_content, file_name, content_type)
    except Exception as e:
        print(f"❌ خطأ في رفع الملف باستخدام Firebase Web API: {e}")
        return None

if __name__ == "__main__":
    # اختبار Firebase Web API
    print("🚀 اختبار Firebase Web API...")
    
    uploader = FirebaseWebUploader()
    test_result = uploader.test_connection()
    
    if test_result['can_upload']:
        print("🎉 Firebase Web API يعمل بشكل صحيح!")
    else:
        print(f"❌ فشل اختبار Firebase Web API: {test_result.get('error_details', 'خطأ غير معروف')}")
