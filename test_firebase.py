#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار Firebase Storage
"""

import os
import sys

def test_firebase_basic():
    """اختبار Firebase الأساسي"""
    print("🔍 اختبار Firebase Storage...")
    
    # 1. اختبار تحميل Firebase SDK
    try:
        print("🔄 محاولة تحميل Firebase Admin SDK...")
        import firebase_admin
        from firebase_admin import credentials, storage
        print(f"✅ تم تحميل Firebase Admin SDK بنجاح - الإصدار: {firebase_admin.__version__}")
    except ImportError as e:
        print(f"❌ فشل تحميل Firebase Admin SDK: {e}")
        print("💡 لتثبيته استخدم: pip install firebase-admin")
        return False
    except Exception as e:
        print(f"❌ خطأ في تحميل Firebase Admin SDK: {e}")
        return False
    
    # 2. اختبار وجود ملف service account
    service_account_path = "firebase-service-account.json"
    print(f"🔍 فحص ملف service account: {service_account_path}")
    
    if not os.path.exists(service_account_path):
        print(f"❌ ملف service account غير موجود: {service_account_path}")
        return False
    
    print("✅ ملف service account موجود")
    
    # 3. اختبار قراءة ملف service account
    try:
        import json
        with open(service_account_path, 'r') as f:
            config = json.load(f)
        print(f"✅ تم قراءة ملف service account بنجاح")
        print(f"📋 Project ID: {config.get('project_id', 'غير محدد')}")
        print(f"📧 Client Email: {config.get('client_email', 'غير محدد')}")
    except Exception as e:
        print(f"❌ خطأ في قراءة ملف service account: {e}")
        return False
    
    # 4. اختبار تهيئة Firebase
    try:
        print("🔄 محاولة تهيئة Firebase...")
        
        # إغلاق أي تطبيق سابق
        try:
            firebase_admin.delete_app(firebase_admin.get_app())
        except:
            pass
        
        # تهيئة Firebase
        cred = credentials.Certificate(service_account_path)
        app = firebase_admin.initialize_app(cred, {
            'storageBucket': 'download-e33a2.firebasestorage.app'
        })
        
        print("✅ تم تهيئة Firebase بنجاح")
        
        # 5. اختبار Storage
        try:
            print("🔄 محاولة الوصول إلى Storage...")
            bucket = storage.bucket()
            print(f"✅ تم الوصول إلى Storage bucket: {bucket.name}")
            
            # 6. اختبار رفع ملف صغير
            try:
                print("🔄 اختبار رفع ملف صغير...")
                test_blob = bucket.blob("test/firebase_test.txt")
                test_content = b"Firebase connection test - " + str(os.getpid()).encode()
                
                test_blob.upload_from_string(test_content, content_type='text/plain')
                print("✅ تم رفع الملف التجريبي بنجاح")
                
                # جعل الملف عاماً
                test_blob.make_public()
                print("✅ تم جعل الملف عاماً")
                
                # الحصول على رابط التحميل
                public_url = test_blob.public_url
                print(f"🔗 رابط التحميل: {public_url}")
                
                # حذف الملف التجريبي
                try:
                    test_blob.delete()
                    print("✅ تم حذف الملف التجريبي")
                except:
                    print("⚠️ لم يتم حذف الملف التجريبي (ليس مشكلة)")
                
                print("🎉 جميع اختبارات Firebase نجحت!")
                return True
                
            except Exception as upload_error:
                print(f"❌ فشل اختبار رفع الملف: {upload_error}")
                return False
                
        except Exception as storage_error:
            print(f"❌ فشل الوصول إلى Storage: {storage_error}")
            return False
            
    except Exception as init_error:
        print(f"❌ فشل تهيئة Firebase: {init_error}")
        return False

def test_firebase_with_config():
    """اختبار Firebase باستخدام firebase_config.py"""
    print("\n" + "="*50)
    print("🔍 اختبار Firebase باستخدام firebase_config.py...")
    
    try:
        from firebase_config import firebase_manager
        
        # محاولة التهيئة التلقائية
        if firebase_manager.auto_initialize():
            print("✅ تم تهيئة Firebase باستخدام firebase_config.py")
            
            # اختبار الاتصال
            test_result = firebase_manager.test_firebase_connection()
            
            print(f"📊 نتائج الاختبار:")
            print(f"   SDK متوفر: {test_result['sdk_available']}")
            print(f"   مهيأ: {test_result['initialized']}")
            print(f"   Storage متوفر: {test_result['storage_bucket_available']}")
            print(f"   يمكن إنشاء blob: {test_result['can_create_blob']}")
            print(f"   يمكن الرفع: {test_result['can_upload']}")
            
            if test_result['error_details']:
                print(f"   تفاصيل الخطأ: {test_result['error_details']}")
            
            return test_result['can_upload']
        else:
            print("❌ فشل تهيئة Firebase باستخدام firebase_config.py")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار firebase_config.py: {e}")
        return False

if __name__ == "__main__":
    print("🚀 بدء اختبار Firebase Storage...")
    
    # اختبار أساسي
    basic_success = test_firebase_basic()
    
    # اختبار باستخدام firebase_config
    config_success = test_firebase_with_config()
    
    print("\n" + "="*50)
    print("📋 ملخص النتائج:")
    print(f"   الاختبار الأساسي: {'✅ نجح' if basic_success else '❌ فشل'}")
    print(f"   اختبار firebase_config: {'✅ نجح' if config_success else '❌ فشل'}")
    
    if basic_success and config_success:
        print("🎉 Firebase Storage يعمل بشكل صحيح!")
        sys.exit(0)
    else:
        print("❌ هناك مشاكل في Firebase Storage")
        sys.exit(1)
